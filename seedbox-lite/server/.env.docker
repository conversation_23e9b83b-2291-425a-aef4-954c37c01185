# Environment variables for Docker production deployment
NODE_ENV=production
SERVER_PORT=3001
SERVER_HOST=0.0.0.0
FRONTEND_URL=https://seedbox.<domain>
OMDB_API_KEY=trilogy
ACCESS_PASSWORD=seedbox123

# Cache and data directories
CACHE_DIR=/app/cache
DATA_DIR=/app/data
LOG_DIR=/app/logs

# Performance settings
MAX_CACHE_SIZE=5368709120
CLEANUP_INTERVAL=3600000
TORRENT_TIMEOUT=30000

# Security settings
ENABLE_CORS=true
TRUST_PROXY=true
