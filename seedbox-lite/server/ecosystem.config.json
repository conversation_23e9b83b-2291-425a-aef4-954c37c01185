{"apps": [{"name": "seedbox-lite", "script": "index.js", "instances": 1, "exec_mode": "fork", "watch": false, "env_production": {"NODE_ENV": "production", "CLOUD_DEPLOYMENT": "true", "DIGITAL_OCEAN": "true", "DEBUG": "false", "LOG_LEVEL": "1"}, "max_memory_restart": "1G", "exp_backoff_restart_delay": 100, "max_restarts": 10, "autorestart": true, "node_args": "--max-old-space-size=1024", "log_date_format": "YYYY-MM-DD HH:mm Z", "merge_logs": true}]}