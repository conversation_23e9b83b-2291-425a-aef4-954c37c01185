# Docker ignore patterns for frontend
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build output
dist/
build/

# Environment files (except production)
.env
.env.local
.env.development.local
.env.test.local
!.env.production

# Logs
logs
*.log

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Cache directories
.cache/
.parcel-cache/

# Runtime data
pids
*.pid
*.seed
*.pid.lock
