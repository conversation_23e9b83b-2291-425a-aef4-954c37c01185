.layout {
  min-height: 100vh;
  background: #0a0a0a;
}

/* Mobile Header */
.mobile-header {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 64px;
  background: linear-gradient(90deg, #141414 0%, #0f0f0f 100%);
  border-bottom: 1px solid #333333;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.mobile-menu-toggle {
  background: rgba(51, 51, 51, 0.2);
  border: 1px solid #333333;
  color: #e5e5e5;
  cursor: pointer;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-menu-toggle:hover {
  background: rgba(51, 51, 51, 0.4);
  color: #ffffff;
  border-color: #e50914;
  transform: scale(1.05);
}

.mobile-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #4ade80;
  font-weight: 700;
  font-size: 20px;
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #141414 0%, #0f0f0f 100%);
  border-right: 1px solid #333333;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  box-shadow: 2px 0 12px rgba(0, 0, 0, 0.2);
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
  position: relative;
}

.sidebar.collapsed .sidebar-header {
  padding: 24px 12px;
  justify-content: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(45deg, #4ade80, #22c55e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  font-size: 20px;
  transition: all 0.3s;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar.collapsed .logo {
  gap: 0;
}

.sidebar.collapsed .logo span {
  opacity: 0;
  width: 0;
  margin-left: 0;
  transition: all 0.3s;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: #94d3a2;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;
  opacity: 0.7;
  flex-shrink: 0;
}

.sidebar.collapsed .sidebar-toggle {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
}

.collapsed-toggle {
  position: absolute !important;
  top: 50% !important;
  right: 12px !important;
  transform: translateY(-50%) !important;
  padding: 6px !important;
  background: rgba(45, 90, 61, 0.3) !important;
  border: 1px solid #2d5a3d !important;
  border-radius: 6px !important;
}

.collapsed-toggle:hover {
  background: rgba(45, 90, 61, 0.5) !important;
  border-color: #4ade80 !important;
}

.sidebar-toggle:hover {
  background: #2d5a3d;
  color: #4ade80;
  opacity: 1;
}

.desktop-only {
  display: block;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  color: #e5e5e5;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
  border-left: 3px solid transparent;
  margin: 2px 0;
}

.sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 16px 12px;
  border-radius: 8px;
  margin: 4px 8px;
  background: rgba(51, 51, 51, 0.1);
  border-left: none;
}

.sidebar.collapsed .nav-item:hover {
  background: rgba(51, 51, 51, 0.3);
  transform: scale(1.05);
}

.sidebar.collapsed .nav-item.active {
  background: rgba(74, 222, 128, 0.2);
  border: 1px solid rgba(74, 222, 128, 0.3);
  border-radius: 8px;
}

.sidebar.collapsed .nav-item span {
  opacity: 0;
  width: 0;
  overflow: hidden;
  transition: all 0.3s;
}

.nav-item:hover {
  background: rgba(51, 51, 51, 0.3);
  color: #4ade80;
  border-left-color: #4ade80;
}

.nav-item.active {
  background: rgba(74, 222, 128, 0.2);
  color: #4ade80;
  border-left-color: #4ade80;
}

/* Cache Stats */
.cache-stats {
  margin-top: auto;
  margin-bottom: 8px;
  padding: 0 20px;
}

.cache-link {
  display: block;
  background: rgba(51, 51, 51, 0.3);
  border: 1px solid #333333;
  border-radius: 12px;
  padding: 16px;
  text-decoration: none;
  transition: all 0.2s;
  color: inherit;
}

.cache-link:hover {
  background: rgba(51, 51, 51, 0.5);
  border-color: #4ade80;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.1);
}

.cache-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #4ade80;
  font-weight: 600;
  font-size: 14px;
}

.cache-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cache-stat {
  font-size: 12px;
  color: #94d3a2;
}

.disk-usage-mini {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.disk-bar-mini {
  width: 100%;
  height: 6px;
  background: rgba(45, 90, 61, 0.5);
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid #2d5a3d;
}

.disk-fill-mini {
  height: 100%;
  background: linear-gradient(90deg, #4ade80, #22c55e);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.disk-usage-mini span {
  font-size: 11px;
  color: #94d3a2;
  opacity: 0.8;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #2d5a3d;
  transition: all 0.3s;
}

.sidebar.collapsed .sidebar-footer {
  opacity: 0;
  height: 0;
  padding: 0;
  overflow: hidden;
}

.app-info {
  text-align: center;
  color: #94d3a2;
  font-size: 12px;
  line-height: 1.5;
  opacity: 0.8;
}

.app-info p {
  margin: 4px 0;
}

/* Main Content */
.main-content {
  background: #0a0a0a;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
  overflow-x: hidden;
  width: 100%;
  margin-left: 280px; /* Account for sidebar width */
  padding-left: 20px; /* Add padding for visual spacing */
}

/* When sidebar is collapsed, main content expands */
.main-content.expanded {
  margin-left: 80px; /* Account for collapsed sidebar width */
  padding-left: 20px; /* Maintain consistent padding */
}

/* Mobile Overlay */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

/* Enhanced Mobile Responsive */
@media (max-width: 768px) {
  .mobile-header {
    display: flex;
    padding: 0 16px;
    height: 56px;
  }

  .mobile-menu-toggle {
    padding: 10px;
    border-radius: 8px;
  }

  .mobile-logo {
    font-size: 18px;
    gap: 8px;
  }

  .sidebar {
    position: fixed;
    top: 0;
    left: -320px;
    height: 100vh;
    z-index: 999;
    width: 280px !important;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar.mobile-open {
    left: 0;
  }

  .sidebar.collapsed {
    left: -320px;
  }

  .sidebar.mobile-open.collapsed {
    left: 0;
    width: 280px !important;
  }

  .desktop-only {
    display: none;
  }

  .main-content {
    margin-top: 56px;
    width: 100%;
    padding: 0 16px; /* Add mobile padding */
    margin-left: 0;
  }

  .main-content.expanded {
    margin-left: 0;
    padding: 0 16px; /* Maintain mobile padding */
  }

  .mobile-overlay {
    display: block;
  }

  .sidebar-header {
    padding: 20px 16px;
    background: rgba(26, 47, 26, 0.95);
    backdrop-filter: blur(20px);
    min-height: 70px;
  }

  .logo {
    font-size: 18px;
    gap: 8px;
  }

  .nav-item {
    padding: 16px 20px;
    font-size: 15px;
    margin: 2px 12px;
    border-radius: 10px;
    border-left: none;
    background: rgba(45, 90, 61, 0.05);
    transition: all 0.3s ease;
  }

  .nav-item:hover {
    background: rgba(45, 90, 61, 0.2);
    transform: translateX(2px);
  }

  .nav-item.active {
    background: rgba(74, 222, 128, 0.15);
    border: 1px solid rgba(74, 222, 128, 0.3);
    color: #4ade80;
  }

  .nav-icon {
    width: 20px;
    height: 20px;
  }

  .nav-label {
    font-weight: 500;
  }

  .sidebar-footer {
    margin: 12px;
    padding: 12px;
    background: rgba(45, 90, 61, 0.1);
    border-radius: 10px;
    border: 1px solid #2d5a3d;
  }

  .cache-stats {
    gap: 8px;
  }

  .cache-stat {
    padding: 8px;
    border-radius: 6px;
  }

  .cache-label {
    font-size: 10px;
  }

  .cache-value {
    font-size: 12px;
  }
}

/* Desktop layout with proper sidebar margins */
@media (min-width: 769px) {
  .mobile-header {
    display: none;
  }

  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
  }

  .main-content {
    margin-left: 300px; /* Account for sidebar width + spacing */
    margin-top: 0;
  }

  .main-content.expanded {
    margin-left: 100px; /* Account for collapsed sidebar width + spacing */
  }

  .mobile-overlay {
    display: none;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .mobile-header {
    padding: 0 12px;
    height: 52px;
  }

  .mobile-menu-toggle {
    padding: 8px;
  }

  .mobile-logo {
    font-size: 16px;
  }

  .sidebar {
    width: 260px !important;
    left: -260px;
  }

  .sidebar.mobile-open,
  .sidebar.mobile-open.collapsed {
    width: 260px !important;
  }

  .main-content {
    margin-top: 52px;
  }

  .sidebar-header {
    padding: 16px 12px;
    min-height: 60px;
  }

  .logo {
    font-size: 16px;
  }

  .nav-item {
    padding: 14px 16px;
    font-size: 14px;
    margin: 2px 8px;
  }

  .sidebar-footer {
    margin: 8px;
    padding: 10px;
  }

  .cache-stats {
    grid-template-columns: 1fr 1fr;
    gap: 6px;
  }
}
