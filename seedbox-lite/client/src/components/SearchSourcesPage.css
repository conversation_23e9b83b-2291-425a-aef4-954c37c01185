/* SearchSourcesPage.css - Netflix-style Dark Theme */
.search-sources-page {
  padding: 20px 40px 20px calc(10% + 20px); /* 10% padding on the left plus original padding */
  max-width: 1000px;
  margin: 0;
  background: #0a0a0a;
  min-height: 100vh;
  width: 90%; /* Reduce width to 90% to prevent overflow */
  box-sizing: border-box;
  position: relative; /* Add positioning context */
  left: 10%; /* Shift the content right by 10% */
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 24px;
  border-bottom: 1px solid #333;
}

.page-header h1 {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 2rem;
  margin-bottom: 10px;
  color: #fff;
}

.page-header p {
  font-size: 1.1rem;
  color: #ccc;
  margin: 0;
}

.search-sources-container {
  display: grid;
  grid-template-columns: 340px 1fr;
  gap: 20px;
  min-height: 600px;
}

/* Sidebar Styles */
.sources-sidebar {
  background-color: #141414;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  border: 1px solid #333;
}

.sidebar-header {
  padding: 15px;
  background-color: #1a1a1a;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h2 {
  font-size: 1.2rem;
  margin: 0;
  color: #e5e5e5;
}

.add-source-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #4ade80;
  color: #0a0a0a;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.add-source-button:hover {
  background-color: #22c55e;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.3);
}

.sources-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.no-sources {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.source-item {
  background-color: #1a1a1a;
  border-radius: 6px;
  margin-bottom: 10px;
  padding: 12px 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid #333;
  cursor: grab;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.source-item.active {
  border-left: 3px solid #4ade80;
  background-color: #141414;
}

.source-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.source-item.dragging {
  opacity: 0.5;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  background-color: #222;
}

.source-item-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.source-icon {
  font-size: 24px;
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #222;
  border-radius: 50%;
  border: 1px solid #333;
}

.source-details {
  flex: 1;
}

.source-details h3 {
  font-size: 1rem;
  margin: 0 0 5px 0;
  color: #e5e5e5;
}

.source-details p {
  font-size: 0.85rem;
  margin: 0;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.source-actions {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
  padding-top: 5px;
  border-top: 1px solid #333;
}

.source-actions button {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  border-radius: 4px;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.source-actions button:hover {
  background-color: #333;
  color: #4ade80;
  transform: scale(1.1);
}

.sources-footer {
  padding: 15px;
  background-color: #1a1a1a;
  border-top: 1px solid #333;
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.import-button, .export-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #222;
  border: 1px solid #444;
  color: #e5e5e5;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  flex: 1;
  justify-content: center;
  transition: all 0.2s;
}

.import-button:hover, .export-button:hover {
  background-color: #333;
  border-color: #4ade80;
  transform: translateY(-2px);
}

.export-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Content Styles */
.sources-content {
  background-color: #141414;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  border: 1px solid #333;
}

.source-form-container {
  padding: 24px;
}

.source-form-container h2 {
  margin-top: 0;
  margin-bottom: 24px;
  font-size: 1.4rem;
  color: #e5e5e5;
  border-bottom: 1px solid #333;
  padding-bottom: 12px;
}

.source-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  font-size: 0.9rem;
  color: #e5e5e5;
}

.form-group input {
  padding: 12px;
  border: 1px solid #444;
  border-radius: 6px;
  font-size: 1rem;
  background-color: #222;
  color: #e5e5e5;
  transition: all 0.2s;
}

.form-group input:focus {
  border-color: #4ade80;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.2);
}

.form-group small {
  font-size: 0.8rem;
  color: #999;
  margin-top: 4px;
}

.icon-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.icon-option {
  font-size: 1.5rem;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #444;
  border-radius: 6px;
  cursor: pointer;
  background-color: #222;
  transition: all 0.2s;
}

.icon-option:hover {
  background-color: #333;
  transform: scale(1.05);
}

.icon-option.selected {
  background-color: #4ade80;
  border-color: #22c55e;
  color: #0a0a0a;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #333;
}

.cancel-button {
  padding: 12px 20px;
  border: 1px solid #444;
  background-color: #222;
  color: #e5e5e5;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.cancel-button:hover {
  background-color: #333;
}

.save-button {
  padding: 12px 24px;
  border: none;
  background-color: #4ade80;
  color: #0a0a0a;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.2s;
}

.save-button:hover {
  background-color: #22c55e;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.3);
}

/* iFrame Container */
.iframe-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.iframe-header {
  padding: 16px;
  background-color: #1a1a1a;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.iframe-header h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #e5e5e5;
}

.close-iframe {
  background: #222;
  border: 1px solid #444;
  color: #e5e5e5;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.close-iframe:hover {
  background-color: #333;
  border-color: #4ade80;
}

.search-iframe {
  flex: 1;
  border: none;
  width: 100%;
  min-height: 500px;
  background-color: #0a0a0a;
}

.iframe-footer {
  padding: 12px 16px;
  background-color: #1a1a1a;
  border-top: 1px solid #333;
}

.iframe-footer p {
  margin: 0;
  font-size: 0.8rem;
  color: #999;
}

/* Search Instructions */
.search-instructions {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.instructions-content {
  text-align: center;
  max-width: 600px;
  padding: 30px;
  background-color: rgba(26, 26, 26, 0.8);
  border-radius: 12px;
  border: 1px solid #333;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.instructions-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #4ade80;
}

.instructions-content h2 {
  font-size: 1.6rem;
  margin: 0 0 15px 0;
  color: #e5e5e5;
  font-weight: 600;
}

.instructions-content p {
  color: #999;
  margin-bottom: 30px;
  line-height: 1.6;
}

.instruction-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 30px 0;
  text-align: left;
}

.step {
  display: flex;
  gap: 15px;
}

.step-number {
  width: 32px;
  height: 32px;
  background-color: #4ade80;
  color: #0a0a0a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(74, 222, 128, 0.3);
}

.step-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  color: #e5e5e5;
}

.step-content p {
  margin: 0;
  color: #999;
}

.instructions-note {
  margin-top: 40px;
  padding: 16px;
  background-color: rgba(74, 222, 128, 0.1);
  border: 1px solid rgba(74, 222, 128, 0.3);
  border-radius: 8px;
}

.instructions-note p {
  margin: 0;
  font-size: 0.9rem;
  color: #bbb;
}

/* Import/Export Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.import-export-modal {
  background-color: #141414;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 1px solid #333;
}

.import-export-modal h2 {
  margin-top: 0;
  margin-bottom: 24px;
  font-size: 1.3rem;
  color: #e5e5e5;
  padding-bottom: 12px;
  border-bottom: 1px solid #333;
}

.import-export-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.import-export-content textarea {
  width: 100%;
  min-height: 220px;
  padding: 12px;
  border: 1px solid #444;
  border-radius: 6px;
  font-family: monospace;
  font-size: 0.9rem;
  resize: vertical;
  background-color: #1a1a1a;
  color: #e5e5e5;
}

.format-help {
  background-color: #222;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #444;
}

.format-help h4 {
  margin: 0 0 12px 0;
  font-size: 0.9rem;
  color: #e5e5e5;
}

.format-help pre {
  margin: 0;
  font-size: 0.8rem;
  overflow-x: auto;
  color: #4ade80;
  padding: 8px;
  background-color: #1a1a1a;
  border-radius: 4px;
}

.modal-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #333;
}

.copy-button, .import-button {
  padding: 12px 24px;
  border: none;
  background-color: #4ade80;
  color: #0a0a0a;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.2s;
}

.copy-button:hover, .import-button:hover {
  background-color: #22c55e;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.3);
}

/* Responsive Styles */
@media (max-width: 900px) {
  .search-sources-container {
    grid-template-columns: 1fr;
  }
  
  .sources-sidebar {
    max-height: 300px;
  }
  
  .sources-content {
    min-height: 500px;
  }
}
