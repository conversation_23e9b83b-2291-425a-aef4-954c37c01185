.torrent-page {
  padding: 24px 24px 24px 0; /* <PERSON>move left padding since main-content has padding now */
  max-width: 1200px;
  margin: 0 auto;
}

.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #333;
  border-top: 3px solid #4ade80;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.torrent-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #333;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #333;
  border: none;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-weight: 500;
}

.back-button:hover {
  background: #444;
  transform: translateY(-1px);
}

.torrent-info {
  flex: 1;
}

.torrent-info h1 {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 700;
  color: #fff;
  line-height: 1.2;
}

.torrent-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  color: #ccc;
}

.stat {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
}

.files-container h2 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #fff;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-item {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease;
}

.file-item:hover {
  border-color: #555;
  background: #1f1f1f;
}

.file-item.video-file {
  border-color: #4ade8033;
  background: linear-gradient(135deg, #1a1a1a, #1a1d1a);
}

.file-item.video-file:hover {
  border-color: #4ade8066;
  background: linear-gradient(135deg, #1f1f1f, #1f221f);
}

.file-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  flex-shrink: 0;
  color: #888;
}

.file-icon.video {
  color: #4ade80;
}

.file-icon.text {
  color: #4f9eff;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #fff;
  margin-bottom: 4px;
  word-break: break-all;
  line-height: 1.3;
}

.file-meta {
  font-size: 13px;
  color: #888;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.progress-info {
  color: #4ade80;
  font-weight: 500;
}

.file-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.play-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #4ade80, #22c55e);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.play-button:hover {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  transform: translateY(-1px);
}

.download-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #e0e0e0;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.download-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: #4ade80;
  color: #4ade80;
  transform: translateY(-1px);
}

.progress-bar {
  margin-top: 12px;
  position: relative;
  height: 6px;
  background: #333;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ade80, #22c55e);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -24px;
  right: 0;
  font-size: 11px;
  color: #ccc;
  font-weight: 500;
}

/* Video Overlay */
.video-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0; /* Remove any default padding that might cause sizing issues */
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .torrent-page {
    padding: 16px 0; /* Remove horizontal padding on mobile since main-content handles it */
  }
  
  .torrent-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .torrent-info h1 {
    font-size: 24px;
  }
  
  .torrent-stats {
    gap: 12px;
  }
  
  .file-main {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .play-button {
    align-self: flex-start;
  }
  
  /* Fix video overlay mobile responsiveness */
  .video-overlay {
    padding: 0 !important; /* Ensure no padding on mobile */
    margin: 0 !important; /* Ensure no margin on mobile */
  }
}

/* Enhanced UI styles */
.torrent-overview {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.torrent-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  background: #1a1a1a;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #333;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
}

.stat-icon {
  color: #4ade80;
  flex-shrink: 0;
}

.stat-label {
  font-size: 14px;
  color: #ccc;
  font-weight: 500;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
}

.progress-section {
  background: #1a1a1a;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #333;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-label {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.progress-percentage {
  font-size: 18px;
  font-weight: 700;
  color: #4ade80;
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background: #333;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ade80, #22c55e);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #ccc;
}

.ready-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #4ade80;
  font-weight: 500;
}

.spinning {
  animation: spin 1s linear infinite;
}

@media (max-width: 768px) {
  .torrent-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 16px;
  }
  
  .stat-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .progress-section {
    padding: 16px;
  }
  
  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
