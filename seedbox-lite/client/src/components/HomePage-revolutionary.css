/* Revolutionary Sync Bridge Styles */
.revolutionary-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  animation: revolutionary-pulse 2s infinite;
}

.revolutionary-icon {
  width: 16px;
  height: 16px;
  animation: revolutionary-spin 1s linear infinite;
}

.revolutionary-tag {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  margin-left: 8px;
}

.sync-status {
  position: fixed;
  top: 70px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sync-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ff6b35;
  border-radius: 50%;
  animation: sync-spin 1s linear infinite;
}

@keyframes revolutionary-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.5);
  }
}

@keyframes revolutionary-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes sync-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced existing styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  min-height: 100vh;
  color: white;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  color: #4ade80;
}

.logo h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #4ade80, #22c55e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tagline p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
  display: flex;
  align-items: center;
  gap: 8px;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.add-torrent-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.add-torrent-section h2 {
  margin-top: 0;
  margin-bottom: 25px;
  color: #4ade80;
  font-size: 1.5rem;
}

.torrent-form {
  margin-bottom: 20px;
}

.input-group {
  display: flex;
  gap: 12px;
  align-items: stretch;
}

.torrent-input {
  flex: 1;
  padding: 15px 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 16px;
  transition: all 0.3s ease;
}

.torrent-input:focus {
  outline: none;
  border-color: #4ade80;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
}

.torrent-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.add-button {
  padding: 15px 25px;
  background: linear-gradient(135deg, #4ade80, #22c55e);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
}

.add-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 222, 128, 0.3);
}

.add-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.divider {
  text-align: center;
  margin: 20px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.divider span {
  background: rgba(255, 255, 255, 0.1);
  padding: 0 15px;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
}

.file-upload {
  display: flex;
  justify-content: center;
}

.file-upload-label {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 25px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  font-weight: 500;
}

.file-upload-label:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: #4ade80;
  color: #4ade80;
}

.file-input {
  display: none;
}

.recent-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  color: #4ade80;
  font-size: 1.5rem;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-input {
  background: none;
  border: none;
  color: white;
  outline: none;
  width: 200px;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.toggle-history-btn, .clear-history-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.toggle-history-btn:hover, .clear-history-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #4ade80;
}

.clear-history-btn {
  padding: 8px 12px;
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.clear-history-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
}

.recent-torrents {
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.empty-state svg {
  margin-bottom: 20px;
  opacity: 0.4;
}

.empty-state p {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.empty-state small {
  font-size: 0.9rem;
}

.torrent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.torrent-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.torrent-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: #4ade80;
}

.torrent-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.torrent-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: white;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.torrent-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.torrent-size {
  background: rgba(74, 222, 128, 0.2);
  color: #4ade80;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.torrent-source {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .logo h1 {
    font-size: 2rem;
  }

  .input-group {
    flex-direction: column;
  }

  .add-button {
    width: 100%;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .section-actions {
    width: 100%;
    justify-content: space-between;
  }

  .search-input {
    width: 150px;
  }

  .torrent-grid {
    grid-template-columns: 1fr;
  }

  .revolutionary-indicator {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 20px;
  }

  .sync-status {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 20px;
  }
}
