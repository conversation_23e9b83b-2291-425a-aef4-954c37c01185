.home-page {
  padding: 20px 40px 20px calc(10% + 20px); /* 10% padding on the left plus original padding */
  max-width: 1000px;
  margin: 0;
  background: #0a0a0a;
  min-height: 100vh;
  width: 90%; /* Reduce width to 90% to prevent overflow */
  box-sizing: border-box;
  position: relative; /* Add positioning context */
  left: 10%; /* Shift the content right by 10% */
}

/* Hero Section */
.hero-section {
  text-align: center;
  margin-bottom: 60px;
}

.hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.brand {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.brand-icon {
  color: #4ade80;
  filter: drop-shadow(0 0 20px rgba(74, 222, 128, 0.3));
}

.brand-text h1 {
  font-size: 48px;
  font-weight: 800;
  background: linear-gradient(45deg, #4ade80, #22c55e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -1px;
}

.brand-text p {
  font-size: 18px;
  color: #94d3a2;
  margin: 8px 0 0 0;
  font-weight: 500;
}

/* Main Actions Section */
.main-actions {
  max-width: 800px;
  margin: 0 auto 60px auto;
}

/* URL Input Section */
.url-input-section {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 40px;
  border: 1px solid rgba(74, 222, 128, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.url-input-section h2 {
  text-align: center;
  margin: 0 0 24px 0;
  color: #4ade80;
  font-size: 20px;
  font-weight: 600;
}

.url-form {
  max-width: 800px;
  margin: 0 auto;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 8px;
  transition: all 0.3s ease;
}

.input-group:focus-within {
  border-color: #4ade80;
  box-shadow: 0 0 0 4px rgba(74, 222, 128, 0.1);
}

.input-icon {
  color: #94d3a2;
  margin-left: 16px;
  flex-shrink: 0;
}

.url-input {
  flex: 1;
  padding: 16px;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 16px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  box-sizing: border-box;
}

.url-input::placeholder {
  color: #888;
}

/* Base torrent input styles for forms */
.torrent-input {
  flex: 1;
  padding: 16px;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 16px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  box-sizing: border-box;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.torrent-input::placeholder {
  color: #888;
}

.add-button {
  background: linear-gradient(45deg, #4ade80, #22c55e);
  border: none;
  border-radius: 12px;
  padding: 16px 24px;
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
}

.add-button:hover:not(:disabled) {
  background: linear-gradient(45deg, #22c55e, #16a34a);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 222, 128, 0.3);
}

.add-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Compact File Upload Button */
.file-upload-button {
  background: linear-gradient(45deg, #8b5cf6, #7c3aed);
  border: none;
  border-radius: 12px;
  padding: 16px 20px;
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  text-decoration: none;
  font-size: 14px;
}

.file-upload-button:hover:not(.disabled) {
  background: linear-gradient(45deg, #7c3aed, #6d28d9);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.file-upload-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff40;
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Section Divider */
.section-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 32px 0;
  position: relative;
}

.section-divider::before,
.section-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(74, 222, 128, 0.3), transparent);
}

.section-divider span {
  padding: 0 24px;
  background: #0a0a0a;
  color: #94d3a2;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 2px;
}

/* Upload Section */
.upload-section {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  border: 1px solid rgba(74, 222, 128, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.upload-section h2 {
  text-align: center;
  margin: 0 0 24px 0;
  color: #4ade80;
  font-size: 20px;
  font-weight: 600;
}

/* Features Summary */
.features-summary {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  margin-top: 40px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #94d3a2;
  font-size: 16px;
  font-weight: 500;
}

.feature-icon {
  font-size: 20px;
}

@media (max-width: 768px) {
  .home-page {
    margin: 0; /* Reset margin on mobile */
    width: 100%; /* Full width on mobile */
    padding: 20px; /* Even padding */
    left: 0; /* Reset left positioning */
  }
  
  .features-summary {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .brand {
    flex-direction: column;
    gap: 16px;
  }
  
  .brand-text h1 {
    font-size: 36px;
  }
}

.torrent-input:focus {
  outline: none;
  border-color: #4ade80;
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
  background: rgba(255, 255, 255, 0.08);
}

.add-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 18px 32px;
  background: linear-gradient(135deg, #4ade80, #22c55e);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 4px 15px rgba(74, 222, 128, 0.3);
}

.add-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 222, 128, 0.4);
}

.add-button:active {
  transform: translateY(0);
}

.add-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 15px rgba(74, 222, 128, 0.2);
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.divider {
  text-align: center;
  margin: 32px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.divider span {
  background: #0a0a0a;
  padding: 0 20px;
  color: #888;
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 1px;
}

.file-upload-section {
  text-align: center;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 18px 32px;
  background: linear-gradient(135deg, rgba(74, 222, 128, 0.1), rgba(34, 197, 94, 0.1));
  border: 2px dashed #4ade80;
  border-radius: 12px;
  color: #4ade80;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.upload-button:hover:not(.disabled) {
  background: linear-gradient(135deg, rgba(74, 222, 128, 0.2), rgba(34, 197, 94, 0.2));
  border-color: #22c55e;
  color: #22c55e;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 222, 128, 0.2);
}

.upload-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Torrents Section */
.torrents-section {
  margin-bottom: 40px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.torrents-section h2 {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: #fff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
  color: #888;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 12px;
}

.empty-state svg {
  margin-bottom: 16px;
  color: #666;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #ccc;
}

.empty-state p {
  margin: 0;
  color: #888;
}

.torrents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.torrent-card {
  background: linear-gradient(145deg, #1a1a1a, #1f1f1f);
  border: 1px solid #333;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
}

.torrent-card:hover {
  border-color: #555;
  transform: translateY(-2px);
}

.torrent-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 12px;
}

.torrent-name {
  flex: 1;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  cursor: pointer;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  transition: color 0.2s ease;
}

.torrent-name:hover {
  color: #4ade80;
}

.delete-button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #444;
  color: #ccc;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.delete-button:hover {
  background: #d32f2f;
  color: white;
}

.torrent-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ccc;
  font-size: 14px;
}

.stat svg {
  color: #888;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #333;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ade80, #22c55e);
  transition: width 0.3s ease;
}

.progress-text {
  color: #4ade80;
  font-weight: 600;
  font-size: 14px;
  min-width: 50px;
  text-align: right;
}

.torrent-actions {
  display: flex;
  gap: 8px;
}

.view-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #2d5a3d;
  border: none;
  border-radius: 6px;
  color: #94d3a2;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-button:hover {
  background: #4ade80;
  color: #fff;
  transform: translateY(-1px);
}

/* Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .home-page {
    padding: 32px;
  }
  
  .torrents-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .home-page {
    padding: 16px 0; /* Remove horizontal padding on mobile since main-content handles it */
    margin-top: 0;
  }
  
  .hero-section {
    margin-bottom: 32px;
  }
  
  .brand {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
  }
  
  .brand-text h1 {
    font-size: 32px;
    text-align: center;
  }
  
  .brand-text p {
    font-size: 14px;
    text-align: center;
  }
  
  .main-actions {
    margin-bottom: 32px;
  }
  
  .url-input-section {
    padding: 24px 16px;
    margin-bottom: 24px;
    border-radius: 16px;
  }
  
  .url-input-section h2 {
    font-size: 18px;
    margin-bottom: 16px;
  }
  
  .input-group {
    flex-direction: column;
    gap: 0;
    padding: 0;
    background: transparent;
    border: none;
  }
  
  .torrent-input {
    width: 100%;
    padding: 16px;
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    margin-bottom: 16px;
  }
  
  .add-button {
    width: 100%;
    padding: 16px 24px;
    font-size: 16px;
    justify-content: center;
    border-radius: 12px;
  }
  
  .upload-section {
    padding: 24px 16px;
    border-radius: 16px;
  }
  
  .upload-section h2 {
    font-size: 18px;
    margin-bottom: 16px;
  }
  
  .upload-button {
    width: 100%;
    padding: 16px 24px;
    font-size: 16px;
    justify-content: center;
  }
  
  .features-summary {
    flex-direction: column;
    gap: 16px;
    text-align: center;
    padding: 0 16px;
  }
  
  .feature-item {
    padding: 12px;
  }
  
  .torrents-section {
    margin-top: 32px;
  }
  
  .torrents-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .torrent-card {
    padding: 16px;
    border-radius: 12px;
  }
  
  .torrent-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .torrent-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .torrent-name {
    font-size: 14px;
    line-height: 1.4;
  }
  
  .torrent-stats {
    flex-wrap: wrap;
    gap: 8px;
    margin: 8px 0;
  }
  
  .stat {
    font-size: 12px;
    min-width: auto;
  }
  
  .progress-container {
    margin-top: 12px;
  }
}

@media (max-width: 480px) {
  .home-page {
    padding: 12px 8px;
  }
  
  .hero-section {
    margin-bottom: 24px;
  }
  
  .brand {
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .brand-icon {
    width: 32px;
    height: 32px;
  }
  
  .brand-text h1 {
    font-size: 24px;
    letter-spacing: -0.5px;
  }
  
  .brand-text p {
    font-size: 13px;
  }
  
  .main-actions {
    margin-bottom: 24px;
  }
  
  .url-input-section {
    padding: 20px 12px;
    margin-bottom: 20px;
    border-radius: 12px;
  }
  
  .url-input-section h2 {
    font-size: 16px;
    margin-bottom: 12px;
  }
  
  .torrent-input {
    padding: 14px 12px;
    font-size: 16px;
    border-radius: 10px;
    margin-bottom: 12px;
  }
  
  .add-button {
    padding: 14px 20px;
    font-size: 15px;
    border-radius: 10px;
  }
  
  .upload-section {
    padding: 20px 12px;
    border-radius: 12px;
  }
  
  .upload-section h2 {
    font-size: 16px;
    margin-bottom: 12px;
  }
  
  .upload-button {
    padding: 14px 20px;
    font-size: 15px;
    border-radius: 10px;
  }
  
  .features-summary {
    gap: 12px;
    padding: 0 12px;
  }
  
  .feature-item {
    padding: 10px 8px;
    border-radius: 8px;
  }
  
  .feature-icon {
    font-size: 16px;
  }
  
  .feature-item span {
    font-size: 13px;
  }
  
  .torrents-section {
    margin-top: 24px;
  }
  
  .section-header h2 {
    font-size: 18px;
  }
  
  .torrents-grid {
    gap: 8px;
  }
  
  .torrent-card {
    padding: 12px;
    border-radius: 10px;
  }
  
  .torrent-name {
    font-size: 13px;
    line-height: 1.3;
  }
  
  .torrent-stats {
    gap: 6px;
    margin: 6px 0;
    flex-wrap: wrap;
  }
  
  .stat {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
  }
  
  .progress-container {
    margin-top: 8px;
    gap: 8px;
  }
  
  .action-button {
    padding: 8px 12px;
    font-size: 12px;
    min-width: auto;
  }
  
  .empty-state {
    padding: 32px 16px;
  }
  
  .empty-state h3 {
    font-size: 18px;
  }
  
  .empty-state p {
    font-size: 14px;
  }
}

/* History Section */
.history-section {
  margin-bottom: 60px;
  background: #111;
  border: 1px solid #2d2d2d;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  color: #fff;
  font-size: 24px;
  font-weight: 700;
}

.section-header h2 svg {
  color: #4ade80;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-button, .clear-button, .view-all-button {
  padding: 8px 16px;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-button:hover, .view-all-button:hover {
  background: #2a2a2a;
  border-color: #4ade80;
  transform: translateY(-1px);
}

.clear-button {
  background: #dc2626;
  border-color: #dc2626;
}

.clear-button:hover {
  background: #b91c1c;
  transform: translateY(-1px);
}

.search-section {
  margin-bottom: 24px;
}

.search-input {
  position: relative;
  max-width: 400px;
}

.search-input svg {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
}

.search-input input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  color: #fff;
  font-size: 14px;
  transition: border-color 0.2s;
}

.search-input input:focus {
  outline: none;
  border-color: #4ade80;
}

.search-input input::placeholder {
  color: #666;
}

.torrent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.torrent-card {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.torrent-card:hover {
  background: #2a2a2a;
  border-color: #4ade80;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.1);
}

.torrent-info {
  flex: 1;
  min-width: 0;
}

.torrent-info h3 {
  margin: 0 0 8px 0;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.torrent-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.source-tag {
  background: #4ade80;
  color: #000;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.date {
  color: #888;
  font-size: 12px;
}

.torrent-source {
  margin: 0;
  color: #666;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

.remove-button {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.remove-button:hover {
  background: #dc2626;
  color: #fff;
}

.view-all {
  text-align: center;
  margin-top: 24px;
}

.view-all-button {
  background: #4ade80;
  color: #000;
  border-color: #4ade80;
  font-weight: 600;
}

.view-all-button:hover {
  background: #22c55e;
  border-color: #22c55e;
}

@media (max-width: 768px) {
  .history-section {
    padding: 20px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .torrent-grid {
    grid-template-columns: 1fr;
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
.search-sources-link {
  text-align: center;
  margin-top: 20px;
}

.search-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #4ade80;
  font-weight: 500;
  text-decoration: none;
  padding: 10px 16px;
  border-radius: 8px;
  background-color: rgba(74, 222, 128, 0.1);
  transition: all 0.3s;
  border: 1px dashed rgba(74, 222, 128, 0.3);
}

.search-link:hover {
  background-color: rgba(74, 222, 128, 0.2);
  transform: translateY(-2px);
}

/* Media Queries */
@media (max-width: 768px) {
  .search-sources-link {
    margin-top: 15px;
  }
  
  .search-link {
    width: 100%;
    justify-content: center;
    padding: 12px;
  }
}
