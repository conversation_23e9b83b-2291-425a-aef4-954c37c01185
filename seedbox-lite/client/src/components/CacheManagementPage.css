.cache-page {
  padding: 24px 24px 24px 0; /* Remove left padding since main-content has padding now */
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #333;
  flex-wrap: wrap;
  gap: 16px;
}

.header-content {
  flex: 1;
}

.header-content h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #fff;
}

.header-content p {
  margin: 0;
  color: #ccc;
  font-size: 16px;
}

.back-button, .refresh-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  color: #fff;
  text-decoration: none;
  transition: all 0.2s;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.back-button:hover, .refresh-button:hover {
  background: #2a2a2a;
  border-color: #4ade80;
  transform: translateY(-1px);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #ccc;
  font-size: 16px;
}

.cache-section {
  margin-bottom: 40px;
}

.cache-section h2 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Disk Usage */
.disk-usage {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 12px;
  padding: 24px;
}

.disk-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.disk-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #404040;
}

.disk-stat span:first-child {
  color: #ccc;
  font-size: 14px;
}

.disk-stat span:last-child {
  color: #fff;
  font-weight: 600;
}

.disk-bar {
  width: 100%;
  height: 12px;
  background: #2a2a2a;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
  border: 1px solid #404040;
}

.disk-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ade80, #22c55e);
  border-radius: 6px;
  transition: width 0.3s ease;
}

.disk-percentage {
  text-align: center;
  color: #ccc;
  font-size: 14px;
  font-weight: 500;
}

.cache-info {
  margin-top: 16px;
  padding: 12px 16px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #404040;
}

.cache-info p {
  margin: 0;
  color: #ccc;
  font-size: 14px;
  font-style: italic;
  text-align: center;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s;
}

.stat-card:hover {
  border-color: #4ade80;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.1);
}

.stat-card svg {
  color: #4ade80;
  flex-shrink: 0;
}

.stat-card div {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #ccc;
}

/* Bulk Actions */
.bulk-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 1px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.action-button.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-color: #f59e0b;
}

.action-button.warning:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.action-button.danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-color: #ef4444;
}

.action-button.danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Torrents List */
.torrents-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.torrent-item {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  transition: all 0.2s;
}

.torrent-item:hover {
  border-color: #4ade80;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.1);
}

.torrent-info {
  flex: 1;
  min-width: 0;
}

.torrent-info h3 {
  margin: 0 0 8px 0;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.torrent-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #ccc;
}

.torrent-stats span {
  white-space: nowrap;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #2a2a2a;
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid #404040;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ade80, #22c55e);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.torrent-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.view-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #4ade80;
  color: #000;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.view-button:hover {
  background: #22c55e;
  transform: translateY(-1px);
}

.remove-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.remove-button:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #ccc;
}

.empty-state svg {
  color: #4ade80;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .cache-page {
    padding: 16px 0; /* Remove horizontal padding on mobile since main-content handles it */
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .disk-stats {
    grid-template-columns: 1fr;
  }

  .torrent-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .torrent-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .bulk-actions {
    flex-direction: column;
  }

  .action-button {
    width: 100%;
    justify-content: center;
  }
}

/* Progress bar styles */
.progress-container {
  margin-top: 16px;
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #333;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #8b5cf6, #06b6d4);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #ccc;
  text-align: center;
  display: block;
}
