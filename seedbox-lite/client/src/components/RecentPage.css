.recent-page {
  padding: 24px 24px 24px 0; /* <PERSON>move left padding since main-content has padding now */
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #333;
}

.header-content h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #fff;
}

.header-content p {
  margin: 0;
  color: #ccc;
  font-size: 16px;
}

.clear-all-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #d32f2f;
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-button:hover {
  background: #b71c1c;
  transform: translateY(-1px);
}

.stats-section {
  margin-bottom: 32px;
}

.stats-section h2 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #fff;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-card {
  background: linear-gradient(135deg, #1a1a1a, #1f1f1f);
  border: 1px solid #333;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  border-color: #555;
  transform: translateY(-2px);
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #4ade80;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #ccc;
  font-weight: 500;
}

.videos-section {
  margin-top: 32px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #888;
}

.empty-state svg {
  margin-bottom: 16px;
  color: #666;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #ccc;
}

.empty-state p {
  margin: 0 0 24px 0;
  color: #888;
}

.browse-button {
  padding: 12px 24px;
  background: linear-gradient(135deg, #4ade80, #22c55e);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.browse-button:hover {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  transform: translateY(-1px);
}

.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.video-card {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.video-card:hover {
  border-color: #555;
  transform: translateY(-2px);
}

.video-progress-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #333;
  z-index: 1;
}

.video-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ade80, #22c55e);
  transition: width 0.3s ease;
}

.video-content {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.video-info {
  flex: 1;
  min-width: 0;
}

.video-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.video-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.progress-text {
  font-size: 14px;
  color: #4ade80;
  font-weight: 500;
}

.watch-time {
  font-size: 12px;
  color: #888;
}

.progress-percentage {
  font-size: 13px;
  color: #ccc;
  display: flex;
  align-items: center;
  gap: 8px;
}

.completed-badge {
  background: #4caf50;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
}

.video-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.play-action, .torrent-action, .remove-action {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.play-action {
  background: linear-gradient(135deg, #4ade80, #22c55e);
  color: white;
}

.play-action:hover {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  transform: scale(1.1);
}

.torrent-action {
  background: #333;
  color: #ccc;
}

.torrent-action:hover {
  background: #444;
  color: #fff;
}

.remove-action {
  background: #444;
  color: #ccc;
}

.remove-action:hover {
  background: #d32f2f;
  color: white;
}

/* Responsive */
@media (max-width: 768px) {
  .recent-page {
    padding: 16px 0; /* Remove horizontal padding on mobile since main-content handles it */
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-content h1 {
    font-size: 24px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .videos-grid {
    grid-template-columns: 1fr;
  }
  
  .video-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .video-actions {
    flex-direction: row;
    justify-content: flex-end;
  }
}
