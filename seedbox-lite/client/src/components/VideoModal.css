.video-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(12px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(12px);
  }
}

.video-modal {
  width: 100%;
  max-width: 95vw;
  max-height: 95vh;
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.6);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
  from {
    transform: translateY(50px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.video-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  background: rgba(255, 255, 255, 0.03);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

.modal-title h3 {
  color: #e0e0e0;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  max-width: 600px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.modal-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-action-button,
.modal-close-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #e0e0e0;
  border-radius: 10px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-action-button:hover {
  background: rgba(33, 150, 243, 0.2);
  border-color: rgba(33, 150, 243, 0.4);
  transform: translateY(-1px);
}

.modal-close-button:hover {
  background: rgba(244, 67, 54, 0.2);
  border-color: rgba(244, 67, 54, 0.4);
  transform: translateY(-1px);
}

.video-modal-content {
  flex: 1;
  display: flex;
  background: #000;
  min-height: 0; /* Important for flex child */
}

.video-modal-content .video-player-container {
  flex: 1;
  border-radius: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-modal-overlay {
    padding: 10px;
  }
  
  .video-modal {
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 12px;
  }
  
  .video-modal-header {
    padding: 15px 20px;
  }
  
  .modal-title h3 {
    font-size: 1.1rem;
    max-width: 250px;
  }
  
  .modal-action-button,
  .modal-close-button {
    padding: 8px;
  }
}

@media (max-width: 480px) {
  .video-modal-overlay {
    padding: 5px;
  }
  
  .video-modal {
    border-radius: 8px;
  }
  
  .video-modal-header {
    padding: 12px 15px;
  }
  
  .modal-title h3 {
    font-size: 1rem;
    max-width: 200px;
  }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
  .video-modal {
    max-width: 1200px;
    max-height: 90vh;
  }
}

/* Ultra-wide screen optimizations */
@media (min-width: 1600px) {
  .video-modal {
    max-width: 1400px;
  }
}
