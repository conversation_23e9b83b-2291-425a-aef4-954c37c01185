.video-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
}

.video-player-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
}

/* Video Player Close Button - only shown when overlay is hidden */
.video-close-button {
  position: absolute;
  top: 20px;
  right: 20px; /* Back to original position */
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.video-close-button:hover {
  background: rgba(239, 68, 68, 0.9);
  border-color: rgba(239, 68, 68, 0.6);
  transform: scale(1.1);
  box-shadow: 0 6px 24px rgba(239, 68, 68, 0.3);
}

.video-close-button:active {
  transform: scale(1.05);
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Subtitle styling */
.video-element::cue {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 1.2em;
  font-family: Arial, sans-serif;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  line-height: 1.4;
}

.video-element::cue(.large) {
  font-size: 1.4em;
}

.video-element::cue(.small) {
  font-size: 1em;
}

.video-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: white;
  font-size: 1.1rem;
  z-index: 15;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced Torrent Stats Overlay */
.torrent-stats-overlay {
  position: absolute;
  top: 16px;
  left: 16px; /* Moved to left side */
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  color: white;
  font-size: 13px;
  z-index: 1050; /* Higher than video close button */
  min-width: 240px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.network-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-icon {
  flex-shrink: 0;
}

.status-icon.connected {
  color: #22c55e;
}

.status-icon.seeking {
  color: #f59e0b;
  animation: pulse 2s infinite;
}

.status-icon.disconnected {
  color: #ef4444;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text.connected {
  color: #22c55e;
}

.status-text.seeking {
  color: #f59e0b;
}

.status-text.disconnected {
  color: #ef4444;
}

.stats-toggle {
  background: none;
  border: none;
  color: #888;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.stats-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Minimize button for torrent stats */
.stats-minimize {
  background: rgba(239, 68, 68, 0.8); /* Red background to make it clear it's a close button */
  border: 1px solid rgba(239, 68, 68, 0.6);
  color: white;
  cursor: pointer;
  padding: 6px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
  pointer-events: auto; /* Ensure button receives clicks */
  position: relative;
  z-index: 1052; /* Above the overlay */
}

/* Minimize button for torrent stats - smaller and different styling */
.stats-minimize {
  background: rgba(107, 114, 128, 0.8); /* Gray background to differentiate */
  border: 1px solid rgba(107, 114, 128, 0.6);
  color: white;
  cursor: pointer;
  padding: 6px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
  pointer-events: auto;
  position: relative;
  z-index: 1052;
}

.stats-minimize:hover {
  background: rgba(107, 114, 128, 1);
  border-color: rgba(107, 114, 128, 0.8);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

.stats-minimize:active {
  transform: scale(0.95);
}

.stats-minimize:hover {
  background: rgba(239, 68, 68, 1);
  border-color: rgba(239, 68, 68, 0.8);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.stats-minimize:active {
  transform: scale(0.95);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-label {
  color: #ccc;
  font-size: 11px;
  flex: 1;
}

.stat-value {
  color: white;
  font-weight: 600;
  font-size: 12px;
}

/* Buffer Health Indicator */
.buffer-health {
  margin-top: 8px;
}

.buffer-label {
  color: #ccc;
  font-size: 11px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.buffer-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.buffer-fill {
  height: 100%;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.buffer-fill.good {
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

.buffer-fill.medium {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.buffer-fill.poor {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.buffer-percentage {
  color: white;
  font-size: 11px;
  font-weight: 600;
}

/* Buffer Status Overlay */
.buffer-status-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 24px 32px;
  color: white;
  text-align: center;
  z-index: 30;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.6);
  min-width: 280px;
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
}

.buffer-status-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.buffer-status-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #fff;
}

.buffer-status-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.buffer-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.buffer-info-label {
  color: #ccc;
}

.buffer-info-value {
  color: white;
  font-weight: 600;
}

.buffer-health-display {
  margin-top: 16px;
}

.buffer-health-label {
  font-size: 12px;
  color: #ccc;
  margin-bottom: 8px;
}

.buffer-health-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.buffer-health-fill {
  height: 100%;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.buffer-health-fill.good {
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

.buffer-health-fill.medium {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.buffer-health-fill.poor {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.buffer-health-text {
  font-size: 12px;
  font-weight: 600;
  text-align: center;
}

.buffer-health-text.good {
  color: #22c55e;
}

.buffer-health-text.medium {
  color: #f59e0b;
}

.buffer-health-text.poor {
  color: #ef4444;
}

/* Stats Show Button (when hidden) */
.stats-show-button {
  position: absolute;
  top: 16px;
  left: 16px; /* Moved to left side to match overlay position */
  background: rgba(74, 222, 128, 0.2);
  border: 1px solid rgba(74, 222, 128, 0.4);
  border-radius: 10px;
  color: #4ade80;
  padding: 10px;
  cursor: pointer;
  z-index: 1051; /* Higher z-index to be above other elements */
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  pointer-events: auto; /* Ensure button receives clicks */
}

.stats-show-button:hover {
  background: rgba(74, 222, 128, 0.3);
  border-color: rgba(74, 222, 128, 0.6);
  color: #22c55e;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(74, 222, 128, 0.2);
}

.stats-show-button:active {
  transform: scale(1.05);
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 20;
}

.video-controls.visible {
  opacity: 1;
  transform: translateY(0);
}

.video-controls.hidden {
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
}

.controls-background {
  position: absolute;
  top: -60px;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.6) 40%,
    rgba(0, 0, 0, 0.3) 70%,
    transparent 100%
  );
  backdrop-filter: blur(8px);
}

.progress-container {
  position: relative;
  padding: 10px 20px;
  cursor: pointer;
}

.progress-bar {
  position: relative;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: visible;
}

/* Multiple buffered ranges */
.progress-buffered-range {
  position: absolute;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.progress-buffered {
  position: absolute;
  height: 100%;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 3px;
  transition: width 0.1s ease;
}

.progress-played {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.1s ease;
  z-index: 3;
}

/* Torrent download progress overlay */
.progress-torrent {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, rgba(34, 197, 94, 0.3), rgba(22, 163, 74, 0.3));
  border-radius: 3px;
  border-top: 1px solid rgba(34, 197, 94, 0.6);
  transition: width 0.3s ease;
  z-index: 1;
}

.progress-thumb {
  position: absolute;
  width: 14px;
  height: 14px;
  background: #3b82f6;
  border: 2px solid white;
  border-radius: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: left 0.1s ease;
  z-index: 4;
}

.progress-container:hover .progress-thumb {
  transform: translate(-50%, -50%) scale(1.2);
}

/* Progress tooltip */
.progress-tooltip {
  position: absolute;
  top: -35px;
  left: 0;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 5;
}

.progress-container:hover .progress-tooltip {
  opacity: 1;
}

.torrent-progress-text {
  color: #22c55e;
  margin-left: 8px;
}

.progress-container:hover .progress-thumb {
  opacity: 1;
}

.controls-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  position: relative;
}

.controls-left,
.controls-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.controls-center {
  flex: 1;
  text-align: center;
}

.video-title {
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.control-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.play-button {
  background: rgba(76, 175, 80, 0.2);
  border: 2px solid #4CAF50;
}

.play-button:hover {
  background: rgba(76, 175, 80, 0.3);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #4CAF50;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #4CAF50;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.time-display {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
  font-variant-numeric: tabular-nums;
  min-width: 100px;
}

.settings-menu {
  position: relative;
}

.settings-dropdown {
  position: absolute;
  bottom: 120%;
  right: 0;
  background: rgba(20, 20, 20, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px;
  min-width: 150px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.settings-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.settings-section > span {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  font-weight: 600;
  padding: 4px 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 4px;
}

.settings-option {
  background: none;
  border: none;
  color: white;
  padding: 8px 12px;
  text-align: left;
  cursor: pointer;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.settings-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.settings-option.active {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  font-weight: 600;
}

/* Subtitle Menu Styles */
.subtitle-menu {
  position: relative;
}

.subtitle-dropdown {
  position: absolute;
  bottom: 120%;
  right: 0;
  background: rgba(20, 20, 20, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px;
  min-width: 180px;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  z-index: 1000;
}

.subtitle-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.subtitle-section:last-child {
  margin-bottom: 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 8px;
}

.subtitle-section > span {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  font-weight: 600;
  padding: 4px 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 4px;
}

.subtitle-option {
  background: none;
  border: none;
  color: white;
  padding: 8px 12px;
  text-align: left;
  cursor: pointer;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.subtitle-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.subtitle-option.active {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  font-weight: 600;
}

.search-option {
  background: rgba(34, 197, 94, 0.1) !important;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.search-option:hover {
  background: rgba(34, 197, 94, 0.2) !important;
}

.search-option:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

.no-subtitles {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.85rem;
  padding: 8px 12px;
  text-align: center;
  font-style: italic;
}

/* Active subtitle button indicator */
.control-button.active {
  background: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.download-button {
  background: rgba(255, 152, 0, 0.2);
  border: 1px solid rgba(255, 152, 0, 0.5);
}

.download-button:hover {
  background: rgba(255, 152, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .controls-main {
    padding: 12px 15px;
  }
  
  .controls-left,
  .controls-right {
    gap: 8px;
  }
  
  .video-title {
    font-size: 1rem;
    max-width: 200px;
  }
  
  .control-button {
    padding: 6px;
  }
  
  .volume-slider {
    width: 60px;
  }
  
  .time-display {
    font-size: 0.8rem;
    min-width: 80px;
  }
}

/* Fullscreen styles with mobile support */
.video-player-container:fullscreen,
.video-player-container:-webkit-full-screen,
.video-player-container:-moz-full-screen,
.video-player-container:-ms-fullscreen {
  width: 100vw;
  height: 100vh;
  border-radius: 0;
}

.video-player-container:fullscreen .video-element,
.video-player-container:-webkit-full-screen .video-element,
.video-player-container:-moz-full-screen .video-element,
.video-player-container:-ms-fullscreen .video-element {
  width: 100vw;
  height: 100vh;
  object-fit: contain;
}

/* iOS Safari fullscreen video support */
.video-element:-webkit-full-screen {
  width: 100vw;
  height: 100vh;
  object-fit: contain;
  background: #000;
}

/* Mobile Safari specific fullscreen optimizations */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari */
  .video-element {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Force native fullscreen behavior */
  .video-element::-webkit-media-controls-fullscreen-button {
    display: none;
  }
  
  .video-player-container.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 99999 !important;
    background: #000 !important;
  }
  
  .video-player-container.fullscreen .video-element {
    width: 100vw !important;
    height: 100vh !important;
    object-fit: contain !important;
    background: #000 !important;
  }
}

/* Force mobile browsers to hide address bar in fullscreen */
.video-player-container.mobile-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  height: 100dvh !important; /* Dynamic viewport height for mobile */
  z-index: 99999 !important;
  border-radius: 0 !important;
  background: #000 !important;
  /* Mobile Safari optimizations */
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.video-player-container.mobile-fullscreen .video-element {
  width: 100vw !important;
  height: 100vh !important;
  height: 100dvh !important;
  max-height: 100vh !important;
  max-height: 100dvh !important;
  object-fit: contain !important;
  background: #000 !important;
}

/* Additional mobile fullscreen enhancements */
@media (max-width: 768px) {
  .video-player-container.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    border-radius: 0 !important;
    margin: 0 !important; /* Remove any margin */
    padding: 0 !important; /* Remove any padding */
  }
  
  .video-player-container.fullscreen .video-element {
    width: 100vw !important;
    height: 100vh !important;
    object-fit: contain !important;
    margin: 0 !important; /* Remove any margin */
    padding: 0 !important; /* Remove any padding */
  }
  
  /* Ensure controls are visible in mobile fullscreen */
  .video-player-container.fullscreen .video-controls {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    margin: 0 !important; /* Remove any margin that might cause bottom space */
  }
  
  /* Fix non-fullscreen mobile video player sizing */
  .video-player-container:not(.fullscreen) {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .video-player-container:not(.fullscreen) .video-element {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* Enhanced fullscreen button for mobile */
  .fullscreen-button {
    background: rgba(0, 0, 0, 0.8) !important;
    border: 2px solid #4ade80 !important;
    color: #4ade80 !important;
    font-weight: bold !important;
    min-width: 48px !important;
    min-height: 48px !important;
    border-radius: 50% !important;
    position: relative !important;
  }
  
  .fullscreen-button:hover {
    background: rgba(74, 222, 128, 0.2) !important;
    transform: scale(1.1) !important;
  }
  
  .fullscreen-button:active {
    transform: scale(0.95) !important;
    background: rgba(74, 222, 128, 0.4) !important;
  }
  
  /* Add visual feedback for double-tap */
  .video-element {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    touch-action: manipulation;
  }
}

/* Resume Dialog */
.resume-dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.resume-dialog {
  background: linear-gradient(145deg, #1f1f1f, #2a2a2a);
  border: 1px solid #444;
  border-radius: 16px;
  padding: 32px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
}

.resume-dialog h3 {
  margin: 0 0 12px 0;
  color: #fff;
  font-size: 20px;
  font-weight: 700;
}

.resume-dialog p {
  margin: 0 0 20px 0;
  color: #ccc;
  font-size: 16px;
}

.resume-info {
  background: #0f0f0f;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.resume-time {
  color: #4ade80;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.resume-date {
  color: #888;
  font-size: 14px;
}

.resume-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.resume-button {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.resume-button.primary {
  background: linear-gradient(135deg, #4ade80, #22c55e);
  color: white;
}

.resume-button.primary:hover {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  transform: translateY(-1px);
}

.resume-button.secondary {
  background: #333;
  color: #ccc;
  border: 1px solid #555;
}

.resume-button.secondary:hover {
  background: #444;
  color: #fff;
  border-color: #666;
}

/* Responsive for resume dialog */
@media (max-width: 640px) {
  .resume-dialog {
    padding: 24px;
    margin: 20px;
  }
  
  .resume-actions {
    flex-direction: column;
  }
  
  .resume-button {
    min-width: auto;
  }
}
