/* Netflix-style Torrent Page Styles */
.netflix-page {
  width: 100vw;
  min-height: 100vh;
  background: #141414;
  color: #ffffff;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  margin: 0;
  padding: 0 24px;
  overflow-x: hidden;
}

/* Loading State */
.netflix-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #141414;
}

.netflix-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid #333;
  border-top: 3px solid #e50914;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.netflix-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  text-align: center;
  padding: 20px;
}

.netflix-error h2 {
  font-size: 2rem;
  margin-bottom: 16px;
  color: #ffffff;
}

.netflix-error p {
  font-size: 1.1rem;
  color: #b3b3b3;
  margin-bottom: 32px;
  max-width: 400px;
}

.netflix-retry-btn {
  background: #e50914;
  color: white;
  border: none;
  padding: 12px 32px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.netflix-retry-btn:hover {
  background: #f40612;
}

/* Hero Section */
/* Hero Section */
.netflix-hero {
  position: relative;
  height: 70vh; /* Increased height to accommodate the poster better */
  background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.7));
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center; /* Changed from flex-end to center for better vertical alignment */
  width: 100%;
  padding: 40px 48px; /* Increased horizontal padding */
  border-radius: 0;
  margin-left: -24px;
  margin-right: -24px;
  padding-bottom: 60px; /* Add extra padding at the bottom for better alignment */
}

.netflix-hero-content {
  display: flex;
  justify-content: space-between;
  align-items: center; /* Changed from flex-end to center for better vertical alignment */
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.netflix-back-btn {
  position: absolute;
  top: 20px;
  left: 40px; /* Align with hero padding */
  background: rgba(42, 42, 42, 0.8);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.netflix-back-btn:hover {
  background: rgba(42, 42, 42, 0.9);
  transform: translateY(-1px);
}

.netflix-title-section {
  flex: 1;
  max-width: 60%;
}

.netflix-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.1;
}

.netflix-meta {
  margin-bottom: 24px;
}

.netflix-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 1.1rem;
}

.star-icon {
  color: #ffd700;
}

.netflix-votes {
  color: #b3b3b3;
  font-size: 0.9rem;
}

.netflix-info-row {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 1rem;
  color: #b3b3b3;
}

.netflix-info-row span {
  position: relative;
}

.netflix-info-row span:not(:last-child)::after {
  content: '•';
  position: absolute;
  right: -10px;
  color: #666;
}

.netflix-action-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.netflix-play-btn {
  background: #ffffff;
  color: #000000;
  border: none;
  padding: 12px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.netflix-play-btn:hover {
  background: #e6e6e6;
  transform: scale(1.05);
}

.netflix-secondary-btn {
  background: rgba(109, 109, 110, 0.7);
  color: white;
  border: none;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.netflix-secondary-btn:hover {
  background: rgba(109, 109, 110, 0.9);
  transform: translateY(-1px);
}

.netflix-description {
  font-size: 1.2rem;
  line-height: 1.5;
  color: #ffffff;
  max-width: 80%;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.netflix-poster {
  flex-shrink: 0;
  margin-left: 40px;
  display: flex;
  align-items: center;
  height: 100%;
}

.netflix-poster img {
  width: 300px;
  height: 450px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
  margin-bottom: 30px; /* Add space to prevent cropping at top */
}

/* Content Section */
.netflix-content {
  display: flex;
  gap: 40px;
  padding: 0 16px; /* Added horizontal padding */
  max-width: none; /* Use full available width */
  margin: 0 auto;
  width: 100%;
}

.netflix-main-content {
  flex: 2;
}

.netflix-sidebar {
  flex: 1;
  min-width: 300px;
}

.netflix-section {
  margin-bottom: 48px;
  padding: 0 8px;
}

.netflix-section h2 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 24px;
  color: #ffffff;
}

/* Episodes Grid */
.netflix-episodes {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.netflix-episode {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(42, 42, 42, 0.6);
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.netflix-episode:hover {
  background: rgba(42, 42, 42, 0.8);
  transform: translateY(-2px);
}

.netflix-episode-thumbnail {
  position: relative;
  width: 160px;
  height: 90px;
  background: linear-gradient(135deg, #333 0%, #555 100%);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.netflix-episode-play {
  background: rgba(255, 255, 255, 0.9);
  color: #000;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.netflix-episode-play:hover {
  background: #ffffff;
  transform: scale(1.1);
}

.netflix-episode-controls {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.netflix-episode-download {
  background: rgba(229, 9, 20, 0.9);
  color: #fff;
  border: none;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.netflix-episode-download:hover {
  background: #e50914;
  transform: scale(1.1);
}

.netflix-progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0 0 4px 4px;
}

.netflix-progress-fill {
  height: 100%;
  background: #e50914;
  border-radius: 0 0 4px 4px;
  transition: width 0.3s ease;
}

.netflix-episode-info {
  flex: 1;
}

.netflix-episode-actions {
  display: flex;
  align-items: center;
  margin-left: 12px;
}

.netflix-episode-actions .netflix-file-icon {
  width: 32px;
  height: 32px;
  background: rgba(229, 9, 20, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.netflix-episode-actions .netflix-file-icon:hover {
  background: #e50914;
  transform: scale(1.1);
}

.netflix-episode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.netflix-episode-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.netflix-episode-duration {
  font-size: 0.9rem;
  color: #b3b3b3;
}

.netflix-episode-title {
  font-size: 0.95rem;
  color: #b3b3b3;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.netflix-episode-progress {
  font-size: 0.85rem;
  color: #888;
  margin: 0;
}

/* Files Grid */
.netflix-files {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.netflix-file {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(42, 42, 42, 0.6);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.netflix-file:hover {
  background: rgba(42, 42, 42, 0.8);
  transform: translateY(-1px);
}

.netflix-file-icon {
  width: 40px;
  height: 40px;
  background: rgba(229, 9, 20, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.netflix-file-icon:hover {
  background: #e50914;
  transform: scale(1.1);
}

.netflix-file-info {
  flex: 1;
  min-width: 0;
}

.netflix-file-name {
  display: block;
  font-size: 0.95rem;
  color: #ffffff;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.netflix-file-size {
  font-size: 0.85rem;
  color: #b3b3b3;
}

/* Sidebar Info Cards */
.netflix-info-card {
  background: rgba(42, 42, 42, 0.6);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.netflix-info-card h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #ffffff;
}

.netflix-info-card p {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #b3b3b3;
  margin: 0;
}

.netflix-ratings {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.netflix-rating-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.netflix-rating-item:last-child {
  border-bottom: none;
}

.netflix-rating-item span:first-child {
  font-size: 0.9rem;
  color: #b3b3b3;
}

.netflix-rating-item span:last-child {
  font-size: 0.95rem;
  font-weight: 600;
  color: #ffffff;
}

.netflix-torrent-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.netflix-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.netflix-stat:last-child {
  border-bottom: none;
}

.netflix-stat span:first-child {
  font-size: 0.9rem;
  color: #b3b3b3;
}

.netflix-stat span:last-child {
  font-size: 0.95rem;
  font-weight: 600;
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .netflix-hero {
    height: 80vh; /* Slightly taller for tablet */
    margin: -24px -24px 24px -24px; /* Full-width breakout */
    padding: 40px 36px 60px 36px; /* Slightly reduced horizontal padding, maintain bottom padding */
  }
  
  .netflix-back-btn {
    left: 40px; /* Align with hero padding */
  }
  
  .netflix-hero-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 30px;
  }
  
  .netflix-title-section {
    max-width: 100%;
  }
  
  .netflix-poster {
    margin-left: 0;
    align-self: center;
    height: auto;
    margin-top: 20px;
  }
  
  .netflix-poster img {
    width: 200px;
    height: 300px;
    margin-bottom: 0; /* Reset margin for tablet */
  }
  
  .netflix-content {
    flex-direction: column;
    padding: 0; /* No additional padding */
  }
  
  .netflix-sidebar {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .netflix-page {
    padding: 16px 20px; /* Even padding on both sides */
  }
  
  .netflix-hero {
    height: 65vh; /* Slightly increased height for mobile */
    margin: -16px -20px 16px -20px; /* Full-width breakout for mobile */
    padding: 20px 24px 40px 24px; /* Mobile padding with slightly more on sides and bottom */
    align-items: flex-start; /* Better for mobile stacked layout */
    padding-top: 60px; /* Add space for the back button */
  }
  
  .netflix-back-btn {
    left: 20px; /* Mobile position */
  }
  
  .netflix-title {
    font-size: 2.5rem;
  }
  
  .netflix-action-buttons {
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .netflix-secondary-btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
  
  .netflix-content {
    padding: 0 8px; /* Reduced padding for mobile */
  }
  
  .netflix-episode {
    flex-direction: column;
    text-align: center;
  }
  
  .netflix-episode-thumbnail {
    width: 100%;
    height: 200px;
    align-self: center;
  }
  
  .netflix-episode-header {
    justify-content: center;
    flex-direction: column;
    gap: 4px;
  }
  
  .netflix-files {
    grid-template-columns: 1fr;
  }
  
  .netflix-poster img {
    width: 150px;
    height: 225px;
  }
}

@media (max-width: 480px) {
  .netflix-back-btn {
    top: 10px;
    left: 10px; /* Standard small mobile position */
    padding: 8px 12px;
    font-size: 0.8rem;
  }
  
  .netflix-title {
    font-size: 2rem;
  }
  
  .netflix-description {
    font-size: 1rem;
    max-width: 100%;
  }
  
  .netflix-action-buttons {
    justify-content: center;
  }
  
  .netflix-play-btn {
    padding: 10px 24px;
    font-size: 1rem;
  }
  
  .netflix-secondary-btn {
    padding: 8px 16px;
    font-size: 0.85rem;
  }
}

/* Video Overlay - consistent with TorrentPage */
.video-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0; /* Remove any default padding that might cause sizing issues */
  margin: 0; /* Ensure no margin interference */
}

/* Mobile video overlay fixes */
@media (max-width: 768px) {
  .video-overlay {
    padding: 0 !important; /* Force remove padding on mobile */
    margin: 0 !important; /* Force remove margin on mobile */
  }
}
