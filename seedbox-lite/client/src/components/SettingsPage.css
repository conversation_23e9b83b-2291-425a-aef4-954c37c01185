.settings-page {
  padding: 24px 24px 24px 0; /* <PERSON><PERSON>ve left padding since main-content has padding now */
  max-width: 900px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #333;
}

.page-header h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #fff;
}

.page-header p {
  margin: 0;
  color: #ccc;
  font-size: 16px;
}

.settings-section {
  margin-bottom: 40px;
}

.settings-section h2 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 8px;
}

.stat-item {
  background: linear-gradient(135deg, #1a1a1a, #1f1f1f);
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #ccc;
  font-size: 14px;
}

.stat-value {
  color: #4ade80;
  font-weight: 600;
  font-size: 16px;
}

.settings-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-item {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.setting-item label {
  flex: 1;
  min-width: 0; /* Allow label to shrink if needed */
}

.setting-item label span {
  display: block;
  color: #fff;
  font-weight: 600;
  margin-bottom: 4px;
}

.setting-item label p {
  margin: 0;
  color: #ccc;
  font-size: 14px;
  line-height: 1.4;
}

/* Ultra-Compact Toggle Switch Design - Fixed Width */
.switch {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 18px;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: 32px;
  min-width: 32px;
  max-width: 32px;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent; /* Remove blue highlight on mobile */
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #374151;
  border: 1px solid #4b5563;
  transition: all 0.3s ease;
  border-radius: 18px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-tap-highlight-color: transparent;
}

.slider:before {
  position: absolute;
  content: "";
  height: 12px;
  width: 12px;
  left: 2px;
  top: 2px;
  background: #ffffff;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
  background: #10b981;
  border-color: #059669;
  box-shadow: 
    inset 0 1px 3px rgba(16, 185, 129, 0.2),
    0 0 0 1px rgba(16, 185, 129, 0.1);
}

input:checked + .slider:before {
  transform: translateX(14px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Touch-friendly active states */
.switch:active .slider {
  transform: scale(1.02);
}

.switch:active .slider:before {
  width: 14px;
  transition: all 0.1s ease;
}

/* Hover Effects */
.slider:hover {
  border-color: #6b7280;
  background: #4b5563;
}

input:checked + .slider:hover {
  background: #059669;
  border-color: #047857;
}

/* Focus States */
.switch input:focus + .slider {
  outline: 2px solid #10b981;
  outline-offset: 2px;
}

/* Disabled State */
input:disabled + .slider {
  cursor: not-allowed;
  opacity: 0.5;
  background: #374151;
}

input:disabled + .slider:hover {
  border-color: #4b5563;
  background: #374151;
}

/* Select */
.setting-select {
  background: #333;
  border: 1px solid #555;
  border-radius: 6px;
  color: #fff;
  padding: 8px 12px;
  font-size: 14px;
  min-width: 120px;
}

.setting-select:focus {
  outline: none;
  border-color: #4ade80;
}

/* Range Slider */
.setting-slider {
  background: #333;
  height: 6px;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  margin: 8px 0;
  min-width: 120px;
}

.setting-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4ade80;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(74, 222, 128, 0.4);
  transition: all 0.2s ease;
}

.setting-slider::-webkit-slider-thumb:hover {
  background: #22c55e;
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.6);
}

.setting-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4ade80;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(74, 222, 128, 0.4);
  transition: all 0.2s ease;
}

.setting-slider::-moz-range-thumb:hover {
  background: #22c55e;
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.6);
}

.slider-value {
  color: #4ade80;
  font-weight: 600;
  margin-left: 12px;
  min-width: 60px;
  text-align: right;
}

/* Data Actions */
.data-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  position: relative;
}

.action-button.export {
  background: #4caf50;
  color: white;
}

.action-button.export:hover {
  background: #45a049;
  transform: translateY(-1px);
}

.action-button.import {
  background: #2196f3;
  color: white;
}

.action-button.import:hover {
  background: #1976d2;
  transform: translateY(-1px);
}

.action-button.danger {
  background: #d32f2f;
  color: white;
}

.action-button.danger:hover {
  background: #b71c1c;
  transform: translateY(-1px);
}

.action-button.cache-management {
  background: linear-gradient(135deg, #4ade80, #22c55e);
  color: #000;
  text-decoration: none;
  justify-content: space-between;
  position: relative;
}

.action-button.cache-management:hover {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.3);
}

/* About Section */
.about-info {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 24px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.app-info h3 {
  margin: 0 0 8px 0;
  color: #4ade80;
  font-size: 20px;
  font-weight: 700;
}

.app-info p {
  margin: 4px 0;
  color: #ccc;
  line-height: 1.5;
}

.features-list h4 {
  margin: 0 0 12px 0;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}

.features-list ul {
  margin: 0;
  padding: 0 0 0 16px;
  color: #ccc;
}

.features-list li {
  margin-bottom: 6px;
  line-height: 1.4;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .settings-page {
    padding: 16px 0; /* Remove horizontal padding on mobile since main-content handles it */
    margin-top: 0;
  }
  
  .page-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
  }
  
  .page-header h1 {
    font-size: 22px;
    gap: 8px;
  }
  
  .page-header p {
    font-size: 14px;
  }
  
  .settings-section {
    margin-bottom: 32px;
  }
  
  .settings-section h2 {
    font-size: 16px;
    margin-bottom: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
  
  .stat-item {
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .stat-label {
    font-size: 12px;
  }
  
  .stat-value {
    font-size: 14px;
  }
  
  .setting-item {
    padding: 16px;
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .setting-item label {
    order: 1;
  }
  
  .switch {
    order: 2;
    align-self: flex-end;
    margin-top: 8px;
  }
  
  .input-field, .select-field {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
  }
  
  .range-container {
    margin-top: 16px;
  }
  
  .range-input {
    height: 6px;
  }
  
  .about-info {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .data-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .data-actions .button {
    padding: 14px 20px;
    font-size: 16px;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .settings-page {
    padding: 12px 8px;
  }
  
  .page-header h1 {
    font-size: 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .setting-item {
    padding: 12px;
    gap: 12px;
  }
  
  .switch {
    width: 36px;
    height: 20px;
  }
  
  .slider:before {
    height: 14px;
    width: 14px;
  }
  
  input:checked + .slider:before {
    transform: translateX(16px);
  }
}

/* Security Section */
.security-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
}

.security-info {
  margin-bottom: 16px;
}

.security-info p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}
.action-button.search-management {
  background-color: #8b5cf6;
  border: 1px solid #7c3aed;
  color: white;
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 12px;
}

.action-button.search-management:hover {
  background-color: #7c3aed;
}
