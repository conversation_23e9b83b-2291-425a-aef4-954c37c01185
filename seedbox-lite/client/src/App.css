* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Inter', sans-serif;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  background-attachment: fixed;
  color: #e0e0e0;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
  width: 100%;
}

.app {
  min-height: 100vh;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.app-header {
  text-align: center;
  margin-bottom: 2rem;
  color: #e0e0e0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-title {
  text-align: left;
}

.header-title h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-title p {
  margin: 0;
  font-size: 1.1rem;
  color: #aaa;
  font-weight: 300;
}

.settings-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #6c5ce7, #5f3dc4);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.settings-button:hover {
  background: linear-gradient(135deg, #5f3dc4, #4c63d2);
  transform: translateY(-1px);
}

/* Settings Panel Styles */
.settings-panel {
  width: 100%;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  margin-bottom: 2rem;
}

.settings-header h3 {
  margin: 0 0 1.5rem 0;
  color: #e0e0e0;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-section h4 {
  margin: 0 0 1rem 0;
  color: #e0e0e0;
  font-size: 1.1rem;
  font-weight: 600;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.refresh-button {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-button:hover {
  background: linear-gradient(135deg, #2980b9, #1f639a);
  transform: translateY(-1px);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  color: #ccc;
  font-size: 0.9rem;
}

.cache-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-old {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.clear-old:hover {
  background: linear-gradient(135deg, #e67e22, #d35400);
  transform: translateY(-1px);
}

.clear-all {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.clear-all:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-1px);
}

.debug {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
}

.debug:hover {
  background: linear-gradient(135deg, #8e44ad, #732d91);
  transform: translateY(-1px);
}

.auto-clear-settings {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ccc;
  font-size: 0.9rem;
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #4CAF50;
}

.setting-item select {
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #e0e0e0;
  font-size: 0.9rem;
}

.info-text {
  color: #aaa;
  font-size: 0.85rem;
  line-height: 1.5;
}

.info-text p {
  margin: 0.5rem 0;
}

.info-text code {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #4CAF50;
}

/* Update existing header styles - removed duplicate, using new header styles above */

.app-header h1 {
  font-size: 3.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  letter-spacing: -0.02em;
}

.app-header p {
  color: #b0b0b0;
  font-size: 1.2rem;
  font-weight: 300;
}

.main-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
  padding-bottom: 2rem;
}

.torrent-input-section,
.status-section,
.files-section {
  width: 100%;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.input-group {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.torrent-input {
  flex: 1;
  padding: 1.2rem 1.8rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
  font-size: 1rem;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.torrent-input:focus {
  outline: none;
  border-color: #4CAF50;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
}

.torrent-input::placeholder {
  color: #888;
  font-weight: 300;
}

.add-button {
  padding: 1.2rem 2.5rem;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.add-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.add-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.status-card h3 {
  color: #e0e0e0;
  font-size: 1.4rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #b0b0b0;
  font-size: 0.9rem;
}

.status-item svg {
  color: #4CAF50;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.files-section h2 {
  margin-bottom: 1.5rem;
  color: #e0e0e0;
  font-size: 1.5rem;
  font-weight: 600;
}

.file-tree {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.file-tree-item {
  margin-left: 1rem;
}

.file-tree-item:first-child {
  margin-left: 0;
}

.folder-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #e0e0e0;
  font-weight: 500;
}

.folder-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(4px);
}

.folder-item svg {
  color: #FFA726;
}

.folder-contents {
  margin-left: 1.5rem;
  margin-top: 0.5rem;
  border-left: 2px solid rgba(255, 255, 255, 0.1);
  padding-left: 1rem;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.file-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.file-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.file-info svg {
  color: #64B5F6;
}

.file-name {
  font-weight: 500;
  color: #e0e0e0;
  flex: 1;
}

.file-size {
  font-size: 0.85rem;
  color: #999;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.file-actions {
  display: flex;
  gap: 0.75rem;
}

.stream-button, .download-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stream-button {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  color: white;
}

.stream-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
}

.download-button {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
  color: white;
}

.download-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.4);
}

.video-section {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.video-section h2 {
  margin-bottom: 1rem;
  color: #e0e0e0;
  font-size: 1.5rem;
  font-weight: 600;
}

.video-title {
  color: #b0b0b0;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.75rem 1rem;
  border-radius: 8px;
}

.video-container {
  border-radius: 12px;
  overflow: hidden;
  background: #000;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
}

.progress-prompt-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.progress-prompt {
  background: linear-gradient(135deg, #1e1e2e, #2a2a3a);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  max-width: 400px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.progress-prompt svg {
  color: #4CAF50;
  margin-bottom: 1rem;
}

.progress-prompt h3 {
  color: #e0e0e0;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.progress-prompt p {
  color: #b0b0b0;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.prompt-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.resume-button, .restart-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.resume-button {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.restart-button {
  background: rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.resume-button:hover, .restart-button:hover {
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }
  
  .app-header h1 {
    font-size: 2.5rem;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .file-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .prompt-buttons {
    flex-direction: column;
  }
}

/* File upload styles */
.file-upload-section {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 0.95rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  cursor: pointer;
}

.upload-button:hover {
  background: linear-gradient(135deg, #1976D2, #1565C0);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
}

.upload-button:active {
  transform: translateY(0);
}

.torrent-input-section {
  text-align: center;
}

.input-group {
  margin-bottom: 0;
}

/* Torrent Management Styles */
.torrent-management-section {
  width: 100%;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.toggle-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #6c5ce7, #5f3dc4);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-button:hover {
  background: linear-gradient(135deg, #5f3dc4, #4c63d2);
  transform: translateY(-1px);
}

.clear-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-button:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-1px);
}

.torrent-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.torrent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.torrent-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.torrent-item.active {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
}

.torrent-info h4 {
  margin: 0 0 0.5rem 0;
  color: #e0e0e0;
  font-size: 1.1rem;
  font-weight: 600;
}

.torrent-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: #aaa;
}

.torrent-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.switch-button {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.switch-button:hover {
  background: linear-gradient(135deg, #45a049, #3e8e41);
  transform: translateY(-1px);
}

.remove-button {
  padding: 0.5rem;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-button:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-1px);
}

/* Responsive design and overflow handling */
@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }
  
  .main-content {
    gap: 1.5rem;
    padding-bottom: 1rem;
  }
  
  .status-section,
  .files-section {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .app {
    padding: 0.5rem;
  }
  
  .main-content {
    gap: 1rem;
  }
  
  .status-section,
  .files-section {
    padding: 1rem;
  }
}

/* Ensure content can expand without being cropped */
.app {
  position: relative;
  z-index: 1;
}

.main-content {
  position: relative;
  overflow: visible;
}

/* Loading animation for authentication */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
