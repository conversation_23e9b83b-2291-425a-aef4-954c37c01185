# Environment files (keep examples, ignore actual configs)
.env
.env.local
.env.*.local

# Keep example files
!.env.example
!.env.production
!.env.docker

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
/client/dist/
/client/build/

# Runtime files
uploads/
*.torrent
torrents/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Cache
.cache/
.tmp/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache
server/.env.production

# Torrents
downloads
