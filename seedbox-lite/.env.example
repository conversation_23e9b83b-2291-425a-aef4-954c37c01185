# ===========================================
# SEEDBOX LITE ENVIRONMENT CONFIGURATION
# ===========================================

# ===== SERVER CONFIGURATION =====
# Backend server configuration
SERVER_PORT=3000
SERVER_HOST=localhost
SERVER_PROTOCOL=http

# Backend API base URL (used by frontend)
# For development: http://localhost:3000
# For production: https://your-domain.com
VITE_API_BASE_URL=http://localhost:3000

# ===== CORS CONFIGURATION =====
# Frontend URL for CORS (used by backend)
# For development: http://localhost:5173
# For production: https://your-frontend-domain.com
FRONTEND_URL=http://localhost:5173

# ===== EXTERNAL SERVICES =====
# OpenSubtitles API configuration
OPENSUBTITLES_API_URL=https://rest.opensubtitles.org
SUBTITLE_SEEKER_API_URL=https://api.subtitleseeker.com

# ===== DEVELOPMENT/PRODUCTION =====
# Environment mode
NODE_ENV=development

# ===== EXAMPLE CONFIGURATIONS =====
# 
# === LOCAL DEVELOPMENT ===
# SERVER_PORT=3000
# SERVER_HOST=localhost
# SERVER_PROTOCOL=http
# VITE_API_BASE_URL=http://localhost:3000
# FRONTEND_URL=http://localhost:5173
# 
# === DOCKER DEVELOPMENT ===
# SERVER_PORT=3000
# SERVER_HOST=0.0.0.0
# SERVER_PROTOCOL=http
# VITE_API_BASE_URL=http://localhost:3000
# FRONTEND_URL=http://localhost:5173
# 
# === PRODUCTION DEPLOYMENT ===
# SERVER_PORT=3000
# SERVER_HOST=0.0.0.0
# SERVER_PROTOCOL=https
# VITE_API_BASE_URL=https://api.yourdomain.com
# FRONTEND_URL=https://yourdomain.com
# 
# === CUSTOM NETWORK ===
# SERVER_PORT=8080
# SERVER_HOST=*************
# SERVER_PROTOCOL=http
# VITE_API_BASE_URL=http://*************:8080
# FRONTEND_URL=http://*************:3000
