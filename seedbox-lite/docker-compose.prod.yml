# Production Docker Compose configuration
version: '3.8'

services:
  seedbox-backend:
    environment:
      - NODE_ENV=production
      - SERVER_PORT=3001
      - SERVER_HOST=0.0.0.0
      - FRONTEND_URL=https://<domain>
      - ACCESS_PASSWORD=${ACCESS_PASSWORD:-sadsgasgdkjh}
    env_file:
      - ./server/.env.docker

  seedbox-frontend:
    build:
      args:
        - VITE_API_BASE_URL=https://apidomain.com
    env_file:
      - ./client/.env.production

  # Enable nginx for production
  nginx:
    profiles:
      - production
    ports:
      - "80:80"
      - "443:443"
