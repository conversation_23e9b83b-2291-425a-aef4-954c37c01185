version: '3.8'

services:
  # Backend API Server
  seedbox-backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: seedbox-backend
    restart: unless-stopped
    ports:
      - "${BACKEND_PORT:-3001}:3001"
    env_file:
      - .env
    volumes:
      - seedbox_data:/app/data
      - seedbox_cache:/app/cache
    networks:
      - seedbox-network

  # Frontend Web Server  
  seedbox-frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:3001}
    container_name: seedbox-frontend
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-5174}:80"
    depends_on:
      - seedbox-backend
    networks:
      - seedbox-network
volumes:
  seedbox_data:
    driver: local
  seedbox_cache:
    driver: local

networks:
  seedbox-network:
    driver: bridge
