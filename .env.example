# Server Configuration
PORT=3000
NODE_ENV=development

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
SESSION_SECRET=your-session-secret-key

# Database
DB_PATH=./data/seedbox.db

# File Storage
DOWNLOADS_PATH=./downloads
TEMP_PATH=./temp
MAX_UPLOAD_SIZE=100mb

# Torrent Settings
MAX_CONCURRENT_DOWNLOADS=5
DOWNLOAD_SPEED_LIMIT=0
UPLOAD_SPEED_LIMIT=0
SEED_RATIO_LIMIT=2.0

# Media Settings
ENABLE_TRANSCODING=true
VIDEO_QUALITY=720p
AUDIO_BITRATE=128k

# User Management
ENABLE_REGISTRATION=true
DEFAULT_USER_QUOTA=100gb
ADMIN_EMAIL=<EMAIL>

# Security Settings
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
ENABLE_CORS=true
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/seedbox.log

# WebSocket
WS_PORT=8080

# External Services (Optional)
TMDB_API_KEY=your-tmdb-api-key-for-metadata
OMDB_API_KEY=your-omdb-api-key-for-metadata
